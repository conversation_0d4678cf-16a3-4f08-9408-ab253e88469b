/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersActivityMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CoreTransfersActivityServiceTest {

  @Mock private CoreTransfersActivityServiceAsync coreTransfersActivityServiceAsync;
  @Mock private CoreTransfersActivityMapper coreTransfersActivityMapper;

  private CoreTransfersActivityService coreTransfersActivityService;

  @BeforeEach
  void setUp() {
    coreTransfersActivityService =
        new CoreTransfersActivityService(
            coreTransfersActivityServiceAsync, coreTransfersActivityMapper);
  }

  @Test
  void getCoreTransfersActivitiesAsync_WithValidResponse_ShouldReturnMappedActivities()
      throws ExecutionException, InterruptedException {
    // Given
    TDSCoreTransferActivityResponse mockResponse = createMockCoreTransferResponse();
    List<BffActivity> mappedActivities = createMockMappedActivities();

    when(coreTransfersActivityServiceAsync.getCoreTransferActivityAsync())
        .thenReturn(CompletableFuture.completedFuture(mockResponse));
    when(coreTransfersActivityMapper.mapCoreTransferActivities(
            mockResponse.getTransferActivities()))
        .thenReturn(mappedActivities);

    // When
    CompletableFuture<List<BffActivity>> result =
        coreTransfersActivityService.getCoreTransfersActivitiesAsync();
    List<BffActivity> activities = result.get();

    // Then
    assertNotNull(activities);
    assertEquals(2, activities.size());
    assertEquals("mapped-1", activities.get(0).getId());
    assertEquals("mapped-2", activities.get(1).getId());
    verify(coreTransfersActivityServiceAsync, times(1)).getCoreTransferActivityAsync();
    verify(coreTransfersActivityMapper, times(1)).mapCoreTransferActivities(any());
  }

  @Test
  void getCoreTransfersActivitiesAsync_WithNullResponse_ShouldReturnEmptyList()
      throws ExecutionException, InterruptedException {
    // Given
    when(coreTransfersActivityServiceAsync.getCoreTransferActivityAsync())
        .thenReturn(CompletableFuture.completedFuture(null));

    // When
    CompletableFuture<List<BffActivity>> result =
        coreTransfersActivityService.getCoreTransfersActivitiesAsync();
    List<BffActivity> activities = result.get();

    // Then
    assertNotNull(activities);
    assertTrue(activities.isEmpty());
    verify(coreTransfersActivityServiceAsync, times(1)).getCoreTransferActivityAsync();
    verify(coreTransfersActivityMapper, never()).mapCoreTransferActivities(any());
  }

  @Test
  void getCoreTransfersActivitiesAsync_WithEmptyTransferActivities_ShouldReturnEmptyList()
      throws ExecutionException, InterruptedException {
    // Given
    TDSCoreTransferActivityResponse mockResponse = new TDSCoreTransferActivityResponse();
    mockResponse.setTransferActivities(null);

    when(coreTransfersActivityServiceAsync.getCoreTransferActivityAsync())
        .thenReturn(CompletableFuture.completedFuture(mockResponse));

    // When
    CompletableFuture<List<BffActivity>> result =
        coreTransfersActivityService.getCoreTransfersActivitiesAsync();
    List<BffActivity> activities = result.get();

    // Then
    assertNotNull(activities);
    assertTrue(activities.isEmpty());
    verify(coreTransfersActivityServiceAsync, times(1)).getCoreTransferActivityAsync();
    verify(coreTransfersActivityMapper, never()).mapCoreTransferActivities(any());
  }

  @Test
  void getCoreTransfersActivitiesAsync_WhenMappingThrowsException_ShouldReturnEmptyList()
      throws ExecutionException, InterruptedException {
    // Given
    TDSCoreTransferActivityResponse mockResponse = createMockCoreTransferResponse();

    when(coreTransfersActivityServiceAsync.getCoreTransferActivityAsync())
        .thenReturn(CompletableFuture.completedFuture(mockResponse));
    when(coreTransfersActivityMapper.mapCoreTransferActivities(any()))
        .thenThrow(new RuntimeException("Mapping error"));

    // When
    CompletableFuture<List<BffActivity>> result =
        coreTransfersActivityService.getCoreTransfersActivitiesAsync();
    List<BffActivity> activities = result.get();

    // Then
    assertNotNull(activities);
    assertTrue(activities.isEmpty());
    verify(coreTransfersActivityServiceAsync, times(1)).getCoreTransferActivityAsync();
    verify(coreTransfersActivityMapper, times(1)).mapCoreTransferActivities(any());
  }

  @Test
  void getCoreTransfersActivitiesAsync_WhenAsyncCallFails_ShouldReturnEmptyList()
      throws ExecutionException, InterruptedException {
    // Given
    when(coreTransfersActivityServiceAsync.getCoreTransferActivityAsync())
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Service error")));

    // When
    CompletableFuture<List<BffActivity>> result =
        coreTransfersActivityService.getCoreTransfersActivitiesAsync();
    List<BffActivity> activities = result.get();

    // Then
    assertNotNull(activities);
    assertTrue(activities.isEmpty());
    verify(coreTransfersActivityServiceAsync, times(1)).getCoreTransferActivityAsync();
    verify(coreTransfersActivityMapper, never()).mapCoreTransferActivities(any());
  }

  @Test
  void getCoreTransfersActivitiesAsync_ShouldReturnCompletedFuture() {
    // Given
    TDSCoreTransferActivityResponse mockResponse = createMockCoreTransferResponse();
    List<BffActivity> mappedActivities = createMockMappedActivities();

    when(coreTransfersActivityServiceAsync.getCoreTransferActivityAsync())
        .thenReturn(CompletableFuture.completedFuture(mockResponse));
    when(coreTransfersActivityMapper.mapCoreTransferActivities(any())).thenReturn(mappedActivities);

    // When
    CompletableFuture<List<BffActivity>> result =
        coreTransfersActivityService.getCoreTransfersActivitiesAsync();

    // Then
    assertNotNull(result);
    assertFalse(result.isCompletedExceptionally());
  }

  private TDSCoreTransferActivityResponse createMockCoreTransferResponse() {
    TDSCoreTransferActivity activity1 = new TDSCoreTransferActivity();
    activity1.setReferenceId("ref-1");
    activity1.setFromAccountId("from-1");
    activity1.setToAccountId("to-1");
    activity1.setAmount(new BigDecimal("100.00"));
    activity1.setTransferStatus("SUCCESS");
    activity1.setCreatedDate(LocalDate.of(2024, 1, 15));
    activity1.setExpectedPostingDate(LocalDate.of(2024, 1, 15));

    TDSCoreTransferActivity activity2 = new TDSCoreTransferActivity();
    activity2.setReferenceId("ref-2");
    activity2.setFromAccountId("from-2");
    activity2.setToAccountId("to-2");
    activity2.setAmount(new BigDecimal("200.00"));
    activity2.setTransferStatus("VALIDATION_FAILURE");
    activity2.setCreatedDate(LocalDate.of(2024, 1, 16));
    activity2.setExpectedPostingDate(LocalDate.of(2024, 1, 17));

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(activity1, activity2));
    return response;
  }

  private List<BffActivity> createMockMappedActivities() {
    BffActivity activity1 = new BffActivity();
    activity1.setId("mapped-1");
    activity1.setDisplayStatus("Completed");

    BffActivity activity2 = new BffActivity();
    activity2.setId("mapped-2");
    activity2.setDisplayStatus("Unsuccessful");

    return Arrays.asList(activity1, activity2);
  }
}
