/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import com.fitb.digital.bff.movemoney.client.CoreTransfersClient;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CoreTransfersActivityServiceAsync {

  private final CoreTransfersClient coreTransfersClient;

  public CoreTransfersActivityServiceAsync(CoreTransfersClient coreTransfersClient) {
    this.coreTransfersClient = coreTransfersClient;
  }

  @Async
  public CompletableFuture<TDSCoreTransferActivityResponse> getCoreTransferActivityAsync() {
    try {
      TDSCoreTransferActivityResponse response = coreTransfersClient.getTransferActivity();
      log.debug("Core Transfers activity response received: {}", response);
      return CompletableFuture.completedFuture(
          response != null ? response : new TDSCoreTransferActivityResponse());
    } catch (Exception e) {
      log.error("Error calling Core Transfers activity service", e);
      return CompletableFuture.completedFuture(new TDSCoreTransferActivityResponse());
    }
  }
}
