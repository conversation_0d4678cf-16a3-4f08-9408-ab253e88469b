/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.client.CoreTransfersClient;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CoreTransfersActivityServiceAsyncTest {

  @Mock private CoreTransfersClient coreTransfersClient;

  private CoreTransfersActivityServiceAsync service;

  @BeforeEach
  void setUp() {
    service = new CoreTransfersActivityServiceAsync(coreTransfersClient);
  }

  @Test
  void getCoreTransferActivityAsync_WhenClientReturnsValidResponse_ShouldReturnResponse()
      throws ExecutionException, InterruptedException {
    // Given
    TDSCoreTransferActivityResponse expectedResponse = createMockResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(expectedResponse);

    // When
    CompletableFuture<TDSCoreTransferActivityResponse> future =
        service.getCoreTransferActivityAsync();
    TDSCoreTransferActivityResponse result = future.get();

    // Then
    assertNotNull(result);
    assertEquals(1, result.getTransferActivities().size());
    assertEquals("REF123", result.getTransferActivities().get(0).getReferenceId());
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void getCoreTransferActivityAsync_WhenClientThrowsException_ShouldReturnEmptyResponse()
      throws ExecutionException, InterruptedException {
    // Given
    when(coreTransfersClient.getTransferActivity())
        .thenThrow(new RuntimeException("Service error"));

    // When
    CompletableFuture<TDSCoreTransferActivityResponse> future =
        service.getCoreTransferActivityAsync();
    TDSCoreTransferActivityResponse result = future.get();

    // Then
    assertNotNull(result);
    assertTrue(result.getTransferActivities().isEmpty());
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void getCoreTransferActivityAsync_WhenClientReturnsNull_ShouldReturnEmptyResponse()
      throws ExecutionException, InterruptedException {
    // Given
    when(coreTransfersClient.getTransferActivity()).thenReturn(null);

    // When
    CompletableFuture<TDSCoreTransferActivityResponse> future =
        service.getCoreTransferActivityAsync();
    TDSCoreTransferActivityResponse result = future.get();

    // Then
    assertNotNull(result);
    assertTrue(result.getTransferActivities().isEmpty());
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void getCoreTransferActivityAsync_ShouldReturnCompletedFuture() {
    // Given
    TDSCoreTransferActivityResponse expectedResponse = createMockResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(expectedResponse);

    // When
    CompletableFuture<TDSCoreTransferActivityResponse> future =
        service.getCoreTransferActivityAsync();

    // Then
    assertNotNull(future);
    assertTrue(future.isDone());
    assertFalse(future.isCompletedExceptionally());
  }

  private TDSCoreTransferActivityResponse createMockResponse() {
    TDSCoreTransferActivity activity = new TDSCoreTransferActivity();
    activity.setReferenceId("REF123");
    activity.setFromAccountId("FROM_ACCOUNT");
    activity.setFromAccountType("SAVINGS");
    activity.setToAccountId("TO_ACCOUNT");
    activity.setToAccountType("CHECKING");
    activity.setAmount(new BigDecimal("100.50"));
    activity.setTransferStatus("SUCCESS");
    activity.setCreatedDate(LocalDate.of(2024, 1, 15));
    activity.setExpectedPostingDate(LocalDate.of(2024, 1, 15));

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(activity));
    return response;
  }

  @Test
  void getCoreTransferActivityAsync_WhenClientReturnsEmptyResponse_ShouldReturnEmptyResponse()
      throws ExecutionException, InterruptedException {
    // Given
    TDSCoreTransferActivityResponse emptyResponse = new TDSCoreTransferActivityResponse();
    when(coreTransfersClient.getTransferActivity()).thenReturn(emptyResponse);

    // When
    CompletableFuture<TDSCoreTransferActivityResponse> future =
        service.getCoreTransferActivityAsync();
    TDSCoreTransferActivityResponse result = future.get();

    // Then
    assertNotNull(result);
    assertTrue(result.getTransferActivities().isEmpty());
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  @Test
  void getCoreTransferActivityAsync_WhenMultipleActivities_ShouldReturnAllActivities()
      throws ExecutionException, InterruptedException {
    // Given
    TDSCoreTransferActivityResponse response = createMockResponseWithMultipleActivities();
    when(coreTransfersClient.getTransferActivity()).thenReturn(response);

    // When
    CompletableFuture<TDSCoreTransferActivityResponse> future =
        service.getCoreTransferActivityAsync();
    TDSCoreTransferActivityResponse result = future.get();

    // Then
    assertNotNull(result);
    assertEquals(2, result.getTransferActivities().size());
    verify(coreTransfersClient, times(1)).getTransferActivity();
  }

  private TDSCoreTransferActivityResponse createMockResponseWithMultipleActivities() {
    TDSCoreTransferActivity activity1 = new TDSCoreTransferActivity();
    activity1.setReferenceId("REF123");
    activity1.setFromAccountId("FROM_ACCOUNT_1");
    activity1.setAmount(new BigDecimal("100.50"));
    activity1.setTransferStatus("SUCCESS");

    TDSCoreTransferActivity activity2 = new TDSCoreTransferActivity();
    activity2.setReferenceId("REF456");
    activity2.setFromAccountId("FROM_ACCOUNT_2");
    activity2.setAmount(new BigDecimal("250.75"));
    activity2.setTransferStatus("PENDING");

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(activity1, activity2));
    return response;
  }
}
