/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity.coretransfers;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.client.account.responses.InternalAccount;
import com.fitb.digital.bff.movemoney.model.client.account.responses.ListResponse;
import com.fitb.digital.bff.movemoney.service.account.AccountServiceAsync;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersActivityMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CoreTransfersActivityMapperTest {

  @Mock private AccountServiceAsync accountServiceAsync;

  private CoreTransfersActivityMapper mapper;

  @BeforeEach
  void setUp() {
    mapper = new CoreTransfersActivityMapper(accountServiceAsync);

    // Setup default mock behavior for account service
    ListResponse mockAccountListResponse = createMockAccountListResponse();
    when(accountServiceAsync.getAccountListAsync(true))
        .thenReturn(CompletableFuture.completedFuture(mockAccountListResponse));
  }

  @Test
  void mapCoreTransferActivities_WithSuccessStatusAndSameDates_ShouldMapToCompletedActivity() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(List.of(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertEquals("Completed", bffActivity.getDisplayStatus());
    assertEquals("3", bffActivity.getStatus());
    assertEquals("REF123", bffActivity.getId());
    assertEquals("REF123", bffActivity.getDisplayId());
    assertEquals("FROM_ACCOUNT", bffActivity.getFromAccountId());
    assertEquals("TO_ACCOUNT", bffActivity.getToAccountId());
    assertEquals("CHECKING", bffActivity.getToAccountType());
    assertEquals(100.50, bffActivity.getAmount());
    assertEquals(LocalDate.of(2024, 1, 15), bffActivity.getCreationDate());
    assertEquals(LocalDate.of(2024, 1, 15), bffActivity.getDueDate());
    assertEquals(ActivityBase.INTERNAL_TRANSFER, bffActivity.getActivityType());
    assertEquals(ActivityBase.INTERNAL_TRANSFER, bffActivity.getType());

    // Verify account details are populated from account service
    assertEquals("****1234", bffActivity.getFromAccountNumber());
    assertEquals("My Checking Account", bffActivity.getFromAccountName());
    assertEquals("****5678", bffActivity.getToAccountNumber());
    assertEquals("My Savings Account", bffActivity.getToAccountName());
  }

  @Test
  void mapCoreTransferActivities_WithSuccessStatusAndDifferentDates_ShouldMapToScheduledActivity() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 16));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertEquals("Scheduled", bffActivity.getDisplayStatus());
    assertEquals("5", bffActivity.getStatus());
  }

  @Test
  void mapCoreTransferActivities_WithFailureStatus_ShouldMapToUnsuccessfulActivity() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity(
            "VALIDATION_FAILURE", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertEquals("Unsuccessful", bffActivity.getDisplayStatus());
    assertEquals("1", bffActivity.getStatus());
  }

  @Test
  void mapCoreTransferActivities_WithMultipleActivities_ShouldMapAllCorrectly() {
    // Given
    TDSCoreTransferActivity successActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));
    TDSCoreTransferActivity failureActivity =
        createCoreTransferActivity(
            "INVALID_ACC_SOURCE", LocalDate.of(2024, 1, 16), LocalDate.of(2024, 1, 16));

    List<TDSCoreTransferActivity> coreActivities = Arrays.asList(successActivity, failureActivity);

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(coreActivities);

    // Then
    assertEquals(2, result.size());
    assertEquals("Completed", result.get(0).getDisplayStatus());
    assertEquals("Unsuccessful", result.get(1).getDisplayStatus());
  }

  @Test
  void mapCoreTransferActivities_ShouldSetDefaultValuesForUnmappableFields() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    BffActivity bffActivity = result.get(0);
    // Account details should be populated from account service
    assertEquals("****1234", bffActivity.getFromAccountNumber());
    assertEquals("My Checking Account", bffActivity.getFromAccountName());
    assertEquals("****5678", bffActivity.getToAccountNumber());
    assertEquals("My Savings Account", bffActivity.getToAccountName());
    assertFalse(bffActivity.isExpressDelivery());
    assertNull(bffActivity.getAdditionalPrincipleAmount());
    assertNull(bffActivity.getMemo());
    assertNull(bffActivity.getDeliveryDate());
    assertNull(bffActivity.getEndDate());
    assertNull(bffActivity.getFrequency());
    assertNull(bffActivity.getNumberOfActivities());
    assertNull(bffActivity.getNumberOfRemainingActivities());
    assertFalse(bffActivity.isEditable());
    assertFalse(bffActivity.isCancelable());
    assertFalse(bffActivity.isSeriesTemplate());
  }

  @Test
  void mapCoreTransferActivities_WithNullAmount_ShouldHandleGracefully() {
    // Given
    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));
    coreActivity.setAmount(null);

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    assertNull(result.get(0).getAmount());
  }

  @Test
  void mapCoreTransferActivities_WithNullDates_ShouldHandleGracefully() {
    // Given
    TDSCoreTransferActivity coreActivity = createCoreTransferActivity("SUCCESS", null, null);

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);
    assertNull(bffActivity.getCreationDate());
    assertNull(bffActivity.getDueDate());
    assertNull(bffActivity.getCreateTimestamp());
    assertEquals(
        "Scheduled", bffActivity.getDisplayStatus()); // Null dates are treated as different dates
  }

  @Test
  void mapCoreTransferActivities_WhenAccountServiceFails_ShouldSetAccountDetailsToNull() {
    // Given
    when(accountServiceAsync.getAccountListAsync(true))
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Account service error")));

    TDSCoreTransferActivity coreActivity =
        createCoreTransferActivity("SUCCESS", LocalDate.of(2024, 1, 15), LocalDate.of(2024, 1, 15));

    // When
    List<BffActivity> result = mapper.mapCoreTransferActivities(Arrays.asList(coreActivity));

    // Then
    assertEquals(1, result.size());
    BffActivity bffActivity = result.get(0);

    // Verify account details are null when service fails
    assertNull(bffActivity.getFromAccountNumber());
    assertNull(bffActivity.getFromAccountName());
    assertNull(bffActivity.getToAccountNumber());
    assertNull(bffActivity.getToAccountName());

    // But other fields should still be mapped correctly
    assertEquals("Completed", bffActivity.getDisplayStatus());
    assertEquals("REF123", bffActivity.getId());
  }

  private TDSCoreTransferActivity createCoreTransferActivity(
      String transferStatus, LocalDate createdDate, LocalDate expectedPostingDate) {
    TDSCoreTransferActivity activity = new TDSCoreTransferActivity();
    activity.setReferenceId("REF123");
    activity.setFromAccountId("FROM_ACCOUNT");
    activity.setFromAccountType("SAVINGS");
    activity.setToAccountId("TO_ACCOUNT");
    activity.setToAccountType("CHECKING");
    activity.setAmount(new BigDecimal("100.50"));
    activity.setTransferStatus(transferStatus);
    activity.setCreatedDate(createdDate);
    activity.setExpectedPostingDate(expectedPostingDate);
    return activity;
  }

  private ListResponse createMockAccountListResponse() {
    ListResponse response = new ListResponse();

    // Create mock from account
    InternalAccount fromAccount = new InternalAccount();
    fromAccount.setId("FROM_ACCOUNT");
    fromAccount.setDisplayAccountNumber("****1234");
    fromAccount.setDisplayName("My Checking Account");

    // Create mock to account
    InternalAccount toAccount = new InternalAccount();
    toAccount.setId("TO_ACCOUNT");
    toAccount.setDisplayAccountNumber("****5678");
    toAccount.setDisplayName("My Savings Account");

    response.setAccounts(Arrays.asList(fromAccount, toAccount));
    return response;
  }
}
