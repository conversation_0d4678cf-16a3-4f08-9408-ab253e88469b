/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.service.coretransfers.CoreTransfersActivityMapper;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CoreTransfersActivityService {

  private final CoreTransfersActivityServiceAsync coreTransfersActivityServiceAsync;
  private final CoreTransfersActivityMapper coreTransfersActivityMapper;

  public CoreTransfersActivityService(
      CoreTransfersActivityServiceAsync coreTransfersActivityServiceAsync,
      CoreTransfersActivityMapper coreTransfersActivityMapper) {
    this.coreTransfersActivityServiceAsync = coreTransfersActivityServiceAsync;
    this.coreTransfersActivityMapper = coreTransfersActivityMapper;
  }

  /**
   * Gets Core Transfers activities asynchronously and maps them to BffActivity format
   *
   * @return CompletableFuture containing list of mapped BffActivity objects
   */
  public CompletableFuture<List<BffActivity>> getCoreTransfersActivitiesAsync() {
    return coreTransfersActivityServiceAsync
        .getCoreTransferActivityAsync()
        .thenApply(
            response -> {
              try {
                if (response != null && response.getTransferActivities() != null) {
                  List<BffActivity> mappedActivities =
                      coreTransfersActivityMapper.mapCoreTransferActivities(
                          response.getTransferActivities());
                  log.debug(
                      "Successfully mapped {} Core Transfer activities", mappedActivities.size());
                  return mappedActivities;
                } else {
                  log.debug("Core Transfers response was null or empty");
                  return new ArrayList<BffActivity>();
                }
              } catch (Exception e) {
                log.error("Error mapping Core Transfers activities", e);
                return new ArrayList<BffActivity>();
              }
            })
        .exceptionally(
            throwable -> {
              log.error("Error retrieving Core Transfers activities", throwable);
              return new ArrayList<>();
            });
  }
}
