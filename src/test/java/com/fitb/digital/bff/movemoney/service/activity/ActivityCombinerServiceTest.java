/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.RetrievalErrors;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetActivityResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ActivityCombinerServiceTest {

  @Mock private ActivityServiceV1 activityServiceV1;
  @Mock private CoreTransfersActivityService coreTransfersActivityService;

  private ActivityCombinerService activityCombinerService;

  @BeforeEach
  void setUp() {
    activityCombinerService =
        new ActivityCombinerService(activityServiceV1, coreTransfersActivityService);
  }

  @Test
  void getCombinedActivities_ShouldCallBothServices() {
    // Given - Core Transfers is always enabled when this service is called
    BffGetActivityResponse cesResponse = createMockCESResponse();
    List<BffActivity> coreTransfersActivities = createMockCoreTransfersActivities();

    when(activityServiceV1.getCESActivitiesOnly(5, 10)).thenReturn(cesResponse);
    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenReturn(CompletableFuture.completedFuture(coreTransfersActivities));

    // When
    BffGetActivityResponse result = activityCombinerService.getCombinedActivities(5, 10);

    // Then
    assertNotNull(result);
    assertTrue(result.getRecentActivities().size() >= 2); // At least CES activities
    assertTrue(result.getUpcomingActivities().size() >= 3); // At least CES activities
    verify(activityServiceV1, times(1)).getCESActivitiesOnly(5, 10);
    verify(coreTransfersActivityService, times(1)).getCoreTransfersActivitiesAsync();
  }

  @Test
  void getCombinedActivities_WhenCESFailsButCoreTransfersSucceeds_ShouldReturnCoreTransfersData() {
    // Given
    when(activityServiceV1.getCESActivitiesOnly(5, 10))
        .thenThrow(new RuntimeException("CES error"));

    List<BffActivity> coreTransfersActivities = createMockCoreTransfersActivities();
    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenReturn(CompletableFuture.completedFuture(coreTransfersActivities));

    // When
    BffGetActivityResponse result = activityCombinerService.getCombinedActivities(5, 10);

    // Then
    assertNotNull(result);
    assertTrue(result.getRetrievalErrors().contains(RetrievalErrors.UNABLE_TO_GET_ACTIVITY));
    verify(coreTransfersActivityService, times(1)).getCoreTransfersActivitiesAsync();

    // Should return only Core Transfers activities
    assertEquals(1, result.getRecentActivities().size()); // One completed Core Transfer activity
    assertEquals(1, result.getUpcomingActivities().size()); // One scheduled Core Transfer activity
  }

  @Test
  void getCombinedActivities_WhenCoreTransfersFailsButCESSucceeds_ShouldReturnCESData() {
    // Given
    BffGetActivityResponse cesResponse = createMockCESResponse();
    when(activityServiceV1.getCESActivitiesOnly(5, 10)).thenReturn(cesResponse);
    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Core Transfers error")));

    // When
    BffGetActivityResponse result = activityCombinerService.getCombinedActivities(5, 10);

    // Then
    assertNotNull(result);
    assertTrue(
        result.getRetrievalErrors().contains(RetrievalErrors.RETRIEVAL_ERROR_CORE_TRANSFERS));
    assertEquals(2, result.getRecentActivities().size()); // CES activities only
    assertEquals(3, result.getUpcomingActivities().size()); // CES activities only
  }

  @Test
  void getCombinedActivities_WhenBothServicesFailShouldThrowException() {
    // Given
    when(activityServiceV1.getCESActivitiesOnly(5, 10))
        .thenThrow(new RuntimeException("CES error"));
    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenReturn(CompletableFuture.failedFuture(new RuntimeException("Core Transfers error")));

    // When & Then
    assertThrows(BffException.class, () -> activityCombinerService.getCombinedActivities(5, 10));
  }

  @Test
  void getCombinedActivities_WithNullLimits_ShouldWork() {
    // Given
    BffGetActivityResponse cesResponse = createMockCESResponse();
    when(activityServiceV1.getCESActivitiesOnly(null, null)).thenReturn(cesResponse);
    when(coreTransfersActivityService.getCoreTransfersActivitiesAsync())
        .thenReturn(CompletableFuture.completedFuture(new ArrayList<>()));

    // When
    BffGetActivityResponse result = activityCombinerService.getCombinedActivities(null, null);

    // Then
    assertNotNull(result);
    assertEquals(2, result.getRecentActivities().size());
    assertEquals(3, result.getUpcomingActivities().size());
    verify(activityServiceV1, times(1)).getCESActivitiesOnly(null, null);
  }

  // Helper methods to create mock data
  private BffGetActivityResponse createMockCESResponse() {
    BffGetActivityResponse response = new BffGetActivityResponse();

    List<BffActivity> recentActivities =
        Arrays.asList(
            createMockActivity("ces-1", ActivityBase.COMPLETED_STATUS),
            createMockActivity("ces-2", ActivityBase.COMPLETED_STATUS));

    List<BffActivity> upcomingActivities =
        Arrays.asList(
            createMockActivity("ces-3", ActivityBase.SCHEDULED_STATUS),
            createMockActivity("ces-4", ActivityBase.SCHEDULED_STATUS),
            createMockActivity("ces-5", ActivityBase.SCHEDULED_STATUS));

    response.setRecentActivities(recentActivities);
    response.setUpcomingActivities(upcomingActivities);
    response.setRecurringActivities(new ArrayList<>());
    response.setRetrievalErrors(new ArrayList<>());

    return response;
  }

  private List<BffActivity> createMockCoreTransfersActivities() {
    return Arrays.asList(
        createMockActivity("core-1", ActivityBase.COMPLETED_STATUS),
        createMockActivity("core-2", ActivityBase.SCHEDULED_STATUS));
  }

  private BffActivity createMockActivity(String id, String status) {
    BffActivity activity = new BffActivity();
    activity.setId(id);
    activity.setDisplayStatus(status);
    return activity;
  }
}
