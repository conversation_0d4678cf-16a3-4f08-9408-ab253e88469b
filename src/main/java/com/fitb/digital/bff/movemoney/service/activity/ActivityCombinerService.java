/* Copyright 2024 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static com.fitb.digital.bff.movemoney.service.activity.ActivityUtilities.getRecentList;
import static com.fitb.digital.bff.movemoney.service.activity.ActivityUtilities.getUpcomingList;

import com.fitb.digital.bff.movemoney.exceptions.BffException;
import com.fitb.digital.bff.movemoney.model.ActivityBase;
import com.fitb.digital.bff.movemoney.model.RetrievalErrors;
import com.fitb.digital.bff.movemoney.model.bff.activity.BffActivity;
import com.fitb.digital.bff.movemoney.model.bff.activity.responses.BffGetActivityResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ActivityCombinerService {

  private final ActivityServiceV1 activityServiceV1;
  private final CoreTransfersActivityService coreTransfersActivityService;

  /** Data class to hold service call results */
  private static class ServiceCallResults {
    BffGetActivityResponse cesResponse = null;
    List<BffActivity> coreTransfersActivities = new ArrayList<>();
    boolean cesCallFailed = false;
    boolean coreTransferCallFailed = false;
  }

  public ActivityCombinerService(
      ActivityServiceV1 activityServiceV1,
      CoreTransfersActivityService coreTransfersActivityService) {
    this.activityServiceV1 = activityServiceV1;
    this.coreTransfersActivityService = coreTransfersActivityService;
  }

  /**
   * Gets activities from both CES and Core Transfers in parallel, then combines the responses
   *
   * @param recentLimit limit for recent activities
   * @param upcomingLimit limit for upcoming activities
   * @return combined BffGetActivityResponse
   */
  public BffGetActivityResponse getCombinedActivities(Integer recentLimit, Integer upcomingLimit) {
    // Note: This method is only called when Core Transfers is enabled (controller handles routing)

    // 1. Execute parallel service calls
    ServiceCallResults results = executeParallelServiceCalls(recentLimit, upcomingLimit);

    // 2. Validate service results
    validateServiceResults(results);

    // 3. Create and return combined response
    return createCombinedResponse(results, recentLimit, upcomingLimit);
  }

  /** Executes parallel calls to CES and Core Transfers services */
  private ServiceCallResults executeParallelServiceCalls(
      Integer recentLimit, Integer upcomingLimit) {
    CompletableFuture<BffGetActivityResponse> cesActivityFuture =
        startCESCall(recentLimit, upcomingLimit);
    CompletableFuture<List<BffActivity>> coreTransfersActivityFuture = startCoreTransfersCall();

    return collectServiceResults(cesActivityFuture, coreTransfersActivityFuture);
  }

  /** Starts the CES service call */
  private CompletableFuture<BffGetActivityResponse> startCESCall(
      Integer recentLimit, Integer upcomingLimit) {
    return CompletableFuture.supplyAsync(
        () -> {
          try {
            return activityServiceV1.getCESActivitiesOnly(recentLimit, upcomingLimit);
          } catch (Exception e) {
            log.error("Error calling CES activities", e);
            throw new RuntimeException(e);
          }
        });
  }

  /** Starts the Core Transfers service call */
  private CompletableFuture<List<BffActivity>> startCoreTransfersCall() {
    return coreTransfersActivityService.getCoreTransfersActivitiesAsync();
  }

  /** Collects results from both service calls */
  private ServiceCallResults collectServiceResults(
      CompletableFuture<BffGetActivityResponse> cesActivityFuture,
      CompletableFuture<List<BffActivity>> coreTransfersActivityFuture) {

    ServiceCallResults results = new ServiceCallResults();

    // Get CES response
    try {
      results.cesResponse = cesActivityFuture.get();
    } catch (Exception ex) {
      results.cesCallFailed = true;
      log.error("Error retrieving CES activities", ex);
    }

    // Get Core Transfers response (always enabled when this service is called)
    try {
      results.coreTransfersActivities = coreTransfersActivityFuture.get();
    } catch (Exception ex) {
      results.coreTransferCallFailed = true;
      log.error("Error retrieving Core Transfers activities", ex);
    }

    return results;
  }

  /** Validates service call results and throws exception if both critical services failed */
  private void validateServiceResults(ServiceCallResults results) {
    if (results.cesCallFailed && results.coreTransferCallFailed) {
      log.error("Both CES and Core Transfers services failed");
      throw BffException.serviceUnavailable("Cannot access get activity services.");
    }
  }

  /** Creates the combined response based on service call results */
  private BffGetActivityResponse createCombinedResponse(
      ServiceCallResults results, Integer recentLimit, Integer upcomingLimit) {
    if (results.cesCallFailed) {
      return handleCoreTransferActivities(
          results.coreTransfersActivities, recentLimit, upcomingLimit);
    } else {
      return handleCESAndCoreTransfers(results, recentLimit, upcomingLimit);
    }
  }

  /** Handles scenario where CES failed but Core Transfers succeeded */
  private BffGetActivityResponse handleCoreTransferActivities(
      List<BffActivity> coreTransfersActivities, Integer recentLimit, Integer upcomingLimit) {
    BffGetActivityResponse combinedResponse =
        createResponseFromCoreTransfersOnly(coreTransfersActivities);
    limitActivities(
        combinedResponse,
        recentLimit,
        upcomingLimit,
        combinedResponse.getRecentActivities(),
        combinedResponse.getUpcomingActivities());
    combinedResponse.getRetrievalErrors().add(RetrievalErrors.UNABLE_TO_GET_ACTIVITY);
    return combinedResponse;
  }

  /** Handles scenario where CES succeeded (with or without Core Transfers) */
  private BffGetActivityResponse handleCESAndCoreTransfers(
      ServiceCallResults results, Integer recentLimit, Integer upcomingLimit) {
    BffGetActivityResponse combinedResponse = results.cesResponse;

    // If Core Transfers call failed, use CES response only and limit activities
    if (results.coreTransferCallFailed) {
      limitActivities(
          combinedResponse,
          recentLimit,
          upcomingLimit,
          combinedResponse.getRecentActivities(),
          combinedResponse.getUpcomingActivities());
      combinedResponse.getRetrievalErrors().add(RetrievalErrors.RETRIEVAL_ERROR_CORE_TRANSFERS);
    } else if (!results.coreTransfersActivities.isEmpty()) {
      // Merge Core Transfers activities with CES activities
      mergeActivities(
          combinedResponse, results.coreTransfersActivities, recentLimit, upcomingLimit);
    }

    return combinedResponse;
  }

  /** Creates a response containing only Core Transfers activities */
  private BffGetActivityResponse createResponseFromCoreTransfersOnly(
      List<BffActivity> coreTransfersActivities) {

    BffGetActivityResponse response = new BffGetActivityResponse();

    List<BffActivity> recentActivities = new ArrayList<>();
    List<BffActivity> upcomingActivities = new ArrayList<>();

    for (BffActivity activity : coreTransfersActivities) {
      if (ActivityBase.COMPLETED_STATUS.equals(activity.getDisplayStatus())
          || ActivityBase.UNSUCCESSFUL_STATUS.equals(activity.getDisplayStatus())) {
        recentActivities.add(activity);
      } else {
        upcomingActivities.add(activity);
      }
    }

    response.setRecentActivities(recentActivities);
    response.setUpcomingActivities(upcomingActivities);
    response.setRecurringActivities(
        new ArrayList<>()); // No recurring activities in Core Transfers yet

    return response;
  }

  /** Merges Core Transfers activities with existing CES activities */
  private void mergeActivities(
      BffGetActivityResponse combinedResponse,
      List<BffActivity> coreTransfersActivities,
      Integer recentLimit,
      Integer upcomingLimit) {

    // Simple merge - add Core Transfers activities to appropriate lists
    List<BffActivity> recentActivities = new ArrayList<>(combinedResponse.getRecentActivities());
    List<BffActivity> upcomingActivities =
        new ArrayList<>(combinedResponse.getUpcomingActivities());

    for (BffActivity activity : coreTransfersActivities) {
      if (ActivityBase.COMPLETED_STATUS.equals(activity.getDisplayStatus())
          || ActivityBase.UNSUCCESSFUL_STATUS.equals(activity.getDisplayStatus())) {
        recentActivities.add(activity);
      } else {
        upcomingActivities.add(activity);
      }
    }

    limitActivities(
        combinedResponse, recentLimit, upcomingLimit, recentActivities, upcomingActivities);
    log.debug(
        "Merged {} Core Transfers activities with CES activities", coreTransfersActivities.size());
  }

  /** Limits the number of recent and upcoming activities in the response */
  private void limitActivities(
      BffGetActivityResponse response,
      Integer recentLimit,
      Integer upcomingLimit,
      List<BffActivity> recentActivities,
      List<BffActivity> upcomingActivities) {
    response.setRecentActivities(getRecentList(recentActivities));
    response.setUpcomingActivities(getUpcomingList(upcomingActivities));
    ActivityServiceV1.limitActivity(recentLimit, upcomingLimit, response);
  }
}
