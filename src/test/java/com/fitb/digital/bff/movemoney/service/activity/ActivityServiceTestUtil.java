/* Copyright 2022 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.service.activity;

import static com.fitb.digital.bff.movemoney.utils.TestUtils.mockFromFile;

import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import java.util.concurrent.CompletableFuture;

public class ActivityServiceTestUtil {
  protected static CompletableFuture<ClientActivityResponse> mockClientActivityAsyncResponse() {
    try {
      return CompletableFuture.supplyAsync(
          () -> mockFromFile("full_activity_response.json", ClientActivityResponse.class));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  protected static CompletableFuture<ClientActivityResponse>
      mockClientActivityAsyncResponseWithNullDueDate() {
    try {
      return CompletableFuture.supplyAsync(
          () -> mockFromFile("activity_null_due_date.json", ClientActivityResponse.class));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  protected static CompletableFuture<ClientActivityResponse> mockEmptyActivityAsyncResponse() {
    return futureFromClientActivityResponse(
        mockFromFile("empty_activity_response.json", ClientActivityResponse.class));
  }

  protected static CompletableFuture<ClientActivityResponse> futureFromClientActivityResponse(
      ClientActivityResponse response) {
    try {
      return CompletableFuture.supplyAsync(() -> response);
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }
}
