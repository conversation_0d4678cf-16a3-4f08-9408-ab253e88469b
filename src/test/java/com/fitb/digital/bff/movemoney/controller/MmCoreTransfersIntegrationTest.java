/* Copyright 2025 Fifth Third Bankcorp.  All rights reserved. */
package com.fitb.digital.bff.movemoney.controller;

import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.CORE_TRANSFERS_TDS_ENABLED;
import static com.fitb.digital.bff.movemoney.featureflags.FeatureFlagNames.ORCHESTRATOR_ENABLED;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fitb.digital.bff.movemoney.client.CesClient;
import com.fitb.digital.bff.movemoney.client.CoreTransfersClient;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivity;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferActivityResponse;
import com.fitb.digital.bff.movemoney.model.bff.CoreTransfers.responses.TDSCoreTransferResponse;
import com.fitb.digital.bff.movemoney.model.client.activity.responses.ClientActivityResponse;
import com.fitb.digital.lib.featureflag.service.FeatureFlagService;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@SpringBootTest
@AutoConfigureMockMvc
public class MmCoreTransfersIntegrationTest {
  @Autowired private MockMvc mockMvc;

  @MockBean FeatureFlagService featureFlagService;

  @MockBean CoreTransfersClient coreTransfersClient;

  @MockBean CesClient cesClient;

  static final String BEARER_TOKEN =
      "Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6IlFac0xydXlkWEdPQ01qaGk4OXdldGlKa25aVSIsInBpLmF0bSI6Im51eWoifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NsXi0ciImrNR9gsrfQNAO34RKbqMcCPIZjjt8KU3i0xLe86vM_Q4FP8JF8lLdgCJAOUc3lZOTMxD72MPh7TiDJWs7uDZBE7TROIUfSMen4l8mGFRsSF9n4xqThTt93CkdXEoBFh4AuV8SXFYc8vTrDK0cVRWgos918AbpknfYwWxNZZnFzaXsbL_F7wXxx1cQc_GjYb1_ETVmOwKMkIwI_wd-xDbrOC2cOtX0mtxX3Cp3xsYf22324nl3kp8-ObAfcQ4XwzptQ9ReW3XHl3bkbDjPw7ZtA47SNflCgndNXinzEBvL71pwCAlDTdEzcnYHbLUwXSxvaJW-Meri4maSA";

  // Test data constants
  static final String FROM_ACCOUNT_ID = "abf3228b-5066-4729-8e15-b7d44afc0a8a";
  static final String TO_ACCOUNT_ID = "4E82015B-4DF8-ECB1-5FD7-2ECAE0C276E2";
  static final BigDecimal TRANSFER_AMOUNT = new BigDecimal("150.75");
  static final String REFERENCE_ID = "CT-TEST-REF-12345";
  static final String MEMO = "Core Transfer Integration Test";

  @Test
  void coreTransferIntegrationTest_CreateTransferAndValidateActivity() throws Exception {
    // Setup feature flags for Core Transfers
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Step 1: Mock Core Transfer creation response
    TDSCoreTransferResponse createTransferResponse = createMockTransferResponse();
    when(coreTransfersClient.tdsCoreTransfer(any())).thenReturn(createTransferResponse);

    // Step 2: Create the Core Transfer (immediate)
    var createTransferContent = createTransferRequestJson();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(createTransferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.activity").exists())
        .andExpect(MockMvcResultMatchers.jsonPath("$.activity.amount").value(TRANSFER_AMOUNT))
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.activity.fromAccountId").value(FROM_ACCOUNT_ID))
        .andExpect(MockMvcResultMatchers.jsonPath("$.activity.toAccountId").value(TO_ACCOUNT_ID))
        .andExpect(MockMvcResultMatchers.jsonPath("$.activity.memo").value(MEMO));

    // Step 3: Mock activity retrieval responses
    TDSCoreTransferActivityResponse coreTransferActivityResponse = createMockActivityResponse();
    ClientActivityResponse cesActivityResponse = createMockCesActivityResponse();

    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransferActivityResponse);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesActivityResponse);

    // Step 4: Get activities and validate the created transfer appears
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("recentLimit", "10")
                .queryParam("upcomingLimit", "10")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities").isArray())
        .andExpect(MockMvcResultMatchers.jsonPath("$.upcomingActivities").isArray())
        // Validate that our Core Transfer activity is present
        .andExpect(
            MockMvcResultMatchers.jsonPath(
                    "$.recentActivities[?(@.referenceId == '" + REFERENCE_ID + "')]")
                .exists())
        .andExpect(
            MockMvcResultMatchers.jsonPath(
                    "$.recentActivities[?(@.amount == " + TRANSFER_AMOUNT + ")]")
                .exists())
        .andExpect(
            MockMvcResultMatchers.jsonPath(
                    "$.recentActivities[?(@.fromAccountId == '" + FROM_ACCOUNT_ID + "')]")
                .exists())
        .andExpect(
            MockMvcResultMatchers.jsonPath(
                    "$.recentActivities[?(@.toAccountId == '" + TO_ACCOUNT_ID + "')]")
                .exists());

    // Verify that both Core Transfers and CES clients were called
    verify(coreTransfersClient, times(1)).tdsCoreTransfer(any());
    verify(coreTransfersClient, times(1)).getTransferActivity();
    verify(cesClient, times(1)).getTransferAndPayActivity();
  }

  @Test
  void coreTransferIntegrationTest_CreateTransferWithError() throws Exception {
    // Setup feature flags for Core Transfers
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock Core Transfer creation to throw an exception
    when(coreTransfersClient.tdsCoreTransfer(any()))
        .thenThrow(new RuntimeException("Core Transfer service unavailable"));

    var createTransferContent = createTransferRequestJson();

    this.mockMvc
        .perform(
            post("/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .content(createTransferContent)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().is5xxServerError());

    verify(coreTransfersClient, times(1)).tdsCoreTransfer(any());
  }

  private String createTransferRequestJson() {
    return String.format(
        """
        {
            "requestGuid": "core-transfer-test-001",
            "fromAccountId": "%s",
            "toAccountId": "%s",
            "amount": %s,
            "dueDate": "%s",
            "frequency": "ONE_TIME",
            "memo": "%s",
            "activityType": "INTERNAL_TRANSFER",
            "fromAccountType": "DDA",
            "toAccountType": "SAV",
            "isImmediate": true,
            "scheduleImmediately": true
        }""",
        FROM_ACCOUNT_ID, TO_ACCOUNT_ID, TRANSFER_AMOUNT, LocalDate.now(), MEMO);
  }

  private TDSCoreTransferResponse createMockTransferResponse() {
    TDSCoreTransferResponse response = new TDSCoreTransferResponse();
    response.setReferenceId(REFERENCE_ID);
    return response;
  }

  private TDSCoreTransferActivityResponse createMockActivityResponse() {
    TDSCoreTransferActivity activity = new TDSCoreTransferActivity();
    activity.setReferenceId(REFERENCE_ID);
    activity.setFromAccountId(FROM_ACCOUNT_ID);
    activity.setFromAccountType("DDA");
    activity.setToAccountId(TO_ACCOUNT_ID);
    activity.setToAccountType("SAV");
    activity.setAmount(TRANSFER_AMOUNT);
    activity.setTransferStatus("SUCCESS");
    activity.setCreatedDate(LocalDate.now());
    activity.setExpectedPostingDate(LocalDate.now());

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(activity));
    return response;
  }

  private ClientActivityResponse createMockCesActivityResponse() {
    // Return empty CES response to focus on Core Transfers
    return new ClientActivityResponse();
  }

  @Test
  void coreTransferIntegrationTest_ActivityRetrievalWithMultipleTransfers() throws Exception {
    // Setup feature flags for Core Transfers
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock multiple Core Transfer activities
    TDSCoreTransferActivityResponse multipleActivitiesResponse =
        createMockMultipleActivitiesResponse();
    ClientActivityResponse cesActivityResponse = createMockCesActivityResponse();

    when(coreTransfersClient.getTransferActivity()).thenReturn(multipleActivitiesResponse);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesActivityResponse);

    // Get activities and validate multiple transfers are returned
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("recentLimit", "5")
                .queryParam("upcomingLimit", "5")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities").isArray())
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities.length()").value(2))
        // Validate first transfer
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.recentActivities[0].referenceId").value("CT-REF-001"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities[0].amount").value(100.00))
        // Validate second transfer
        .andExpect(
            MockMvcResultMatchers.jsonPath("$.recentActivities[1].referenceId").value("CT-REF-002"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities[1].amount").value(250.50));

    verify(coreTransfersClient, times(1)).getTransferActivity();
    verify(cesClient, times(1)).getTransferAndPayActivity();
  }

  @Test
  void coreTransferIntegrationTest_ActivityRetrievalWithLimits() throws Exception {
    // Setup feature flags for Core Transfers
    when(featureFlagService.isFeatureEnabled(ORCHESTRATOR_ENABLED)).thenReturn(false);
    when(featureFlagService.isFeatureEnabled(CORE_TRANSFERS_TDS_ENABLED)).thenReturn(true);

    // Mock Core Transfer activities
    TDSCoreTransferActivityResponse coreTransferActivityResponse =
        createMockMultipleActivitiesResponse();
    ClientActivityResponse cesActivityResponse = createMockCesActivityResponse();

    when(coreTransfersClient.getTransferActivity()).thenReturn(coreTransferActivityResponse);
    when(cesClient.getTransferAndPayActivity()).thenReturn(cesActivityResponse);

    // Test with specific limits
    this.mockMvc
        .perform(
            get("/v2/activity")
                .header("scope", "openid")
                .header("sub", "mock-mvc-test")
                .header("Authorization", BEARER_TOKEN)
                .header("X-Risk-Authorization", "we-dont-need-no-stinkin-badges")
                .queryParam("recentLimit", "1")
                .queryParam("upcomingLimit", "1")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(MockMvcResultMatchers.jsonPath("$.status").value("SUCCESS"))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities").isArray())
        // With ActivityCombinerService, limiting is handled at the service level
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentActivities.length()").value(1))
        .andExpect(MockMvcResultMatchers.jsonPath("$.recentTruncated").value(true));

    verify(coreTransfersClient, times(1)).getTransferActivity();
    verify(cesClient, times(1)).getTransferAndPayActivity();
  }

  private TDSCoreTransferActivityResponse createMockMultipleActivitiesResponse() {
    TDSCoreTransferActivity activity1 = new TDSCoreTransferActivity();
    activity1.setReferenceId("CT-REF-001");
    activity1.setFromAccountId(FROM_ACCOUNT_ID);
    activity1.setFromAccountType("DDA");
    activity1.setToAccountId(TO_ACCOUNT_ID);
    activity1.setToAccountType("SAV");
    activity1.setAmount(new BigDecimal("100.00"));
    activity1.setTransferStatus("SUCCESS");
    activity1.setCreatedDate(LocalDate.now().minusDays(1));
    activity1.setExpectedPostingDate(LocalDate.now().minusDays(1));

    TDSCoreTransferActivity activity2 = new TDSCoreTransferActivity();
    activity2.setReferenceId("CT-REF-002");
    activity2.setFromAccountId("different-from-account");
    activity2.setFromAccountType("SAV");
    activity2.setToAccountId("different-to-account");
    activity2.setToAccountType("DDA");
    activity2.setAmount(new BigDecimal("250.50"));
    activity2.setTransferStatus("PENDING");
    activity2.setCreatedDate(LocalDate.now());
    activity2.setExpectedPostingDate(LocalDate.now().plusDays(1));

    TDSCoreTransferActivityResponse response = new TDSCoreTransferActivityResponse();
    response.setTransferActivities(Arrays.asList(activity1, activity2));
    return response;
  }
}
